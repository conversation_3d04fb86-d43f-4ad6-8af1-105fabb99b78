# GHG Calculator Backend Integration Guide

This document outlines the steps to migrate the GHG Calculator from localStorage to backend integration.

## Current Architecture

The GHG Calculator currently uses localStorage for data persistence:

- **Storage Key Pattern**: `ghg-calculator-data-{year}` (e.g., `ghg-calculator-data-2024`)
- **Data Structure**: Complete form data + calculated totals stored as JSON
- **Location**: Browser localStorage (client-side only)

## Migration Overview

### Phase 1: API Design & Backend Setup

### Phase 2: Frontend Integration

### Phase 3: Data Migration

### Phase 4: Testing & Deployment

---

## Required APIs

### 1. **Save GHG Data API**

**Endpoint**: `POST /api/ghg-calculator/{year}`

**Purpose**: Save complete GHG calculator data for a specific year

**Request Payload**:

```json
{
  "year": 2024,
  "data": {
    "stationary": {
      "fuelRows": [
        {
          "fuel": "Natural gas",
          "consumption": "1000",
          "unit": "KWH",
          "area": "",
          "areaUnit": ""
        }
      ],
      "biomassRows": [
        {
          "fuel": "Biomass - wood logs",
          "consumption": "500",
          "unit": "M3",
          "area": "",
          "areaUnit": ""
        }
      ]
    },
    "mobile": [
      {
        "category": "Passenger cars",
        "consumption": "2000",
        "fuelType": "PETROL",
        "unit": "LITRES",
        "distance": "",
        "distanceUnit": "",
        "expense": ""
      }
    ],
    "fugitive": [
      {
        "name": "R134a",
        "amount": "10",
        "unit": "KG"
      }
    ],
    "agricultural": {
      "animalRows": [
        {
          "animal": "Dairy Cattle",
          "count": "50",
          "fieldPercentage": "80"
        }
      ],
      "fertiliserRows": [
        {
          "amount": "1000",
          "unit": "KG"
        }
      ]
    },
    "electricity": [
      {
        "type": "non-RES",
        "consumption": "50000",
        "unit": "KWH",
        "cost": "",
        "hasPPA": "NO"
      }
    ],
    "district": [
      {
        "category": "Heating",
        "consumption": "25000",
        "unit": "KWH",
        "area": "",
        "areaUnit": ""
      }
    ],
    "metadata": {
      "lastSaved": "2024-07-15T10:30:00.000Z",
      "version": "1.0"
    },
    "calculatedTotals": {
      "sectionTotals": {
        "stationary": 18313.205,
        "mobile": 4.62,
        "fugitive": 14.3,
        "agricultural": 2.15,
        "scope2Electricity": 11.65,
        "scope2District": 4.95
      },
      "scope1Total": 18334.275,
      "scope2Total": 16.6,
      "totalEmissions": 18350.875
    }
  }
}
```

**Expected Response**:

```json
{
  "success": true,
  "message": "GHG data saved successfully",
  "data": {
    "id": "ghg_2024_user123",
    "year": 2024,
    "lastSaved": "2024-07-15T10:30:00.000Z",
    "calculatedTotals": {
      "scope1Total": 18334.275,
      "scope2Total": 16.6,
      "totalEmissions": 18350.875
    }
  }
}
```

### 2. **Get GHG Data API**

**Endpoint**: `GET /api/ghg-calculator/{year}`

**Purpose**: Retrieve GHG calculator data for a specific year

**Request Parameters**:

- `year` (path parameter): The year to retrieve data for

**Expected Response**:

```json
{
  "success": true,
  "data": {
    "year": 2024,
    "stationary": {
      "fuelRows": [...],
      "biomassRows": [...]
    },
    "mobile": [...],
    "fugitive": [...],
    "agricultural": {
      "animalRows": [...],
      "fertiliserRows": [...]
    },
    "electricity": [...],
    "district": [...],
    "metadata": {
      "lastSaved": "2024-07-15T10:30:00.000Z",
      "version": "1.0"
    },
    "calculatedTotals": {
      "sectionTotals": {...},
      "scope1Total": 18334.275,
      "scope2Total": 16.600,
      "totalEmissions": 18350.875
    }
  }
}
```

**Response when no data exists**:

```json
{
  "success": true,
  "data": null,
  "message": "No data found for year 2024"
}
```

### 3. **List All Years API**

**Endpoint**: `GET /api/ghg-calculator/years`

**Purpose**: Get summary of all years with GHG data

**Expected Response**:

```json
{
  "success": true,
  "data": [
    {
      "year": 2024,
      "scope1Total": 18334.275,
      "scope2Total": 16.6,
      "totalEmissions": 18350.875,
      "lastModified": "2024-07-15T10:30:00.000Z",
      "hasData": true
    },
    {
      "year": 2023,
      "scope1Total": 15200.15,
      "scope2Total": 12.4,
      "totalEmissions": 15212.55,
      "lastModified": "2024-01-20T14:15:00.000Z",
      "hasData": true
    }
  ]
}
```

### 4. **Delete GHG Data API**

**Endpoint**: `DELETE /api/ghg-calculator/{year}`

**Purpose**: Delete GHG calculator data for a specific year

**Expected Response**:

```json
{
  "success": true,
  "message": "GHG data for year 2024 deleted successfully"
}
```

---

## Frontend Integration Steps

### Step 1: Create API Service Layer

Create `src/services/ghgCalculatorApi.ts`:

```typescript
import { GHGFormData } from "@/components/ghg-calculator/EmissionsContext"

export interface YearlyEmissionData {
  year: number
  scope1Total: number
  scope2Total: number
  totalEmissions: number
  lastModified: string
  hasData: boolean
}

export class GHGCalculatorAPI {
  private baseUrl = "/api/ghg-calculator"

  async saveGHGData(year: number, data: GHGFormData): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${year}`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ year, data }),
    })

    if (!response.ok) {
      throw new Error(`Failed to save GHG data: ${response.statusText}`)
    }
  }

  async getGHGData(year: number): Promise<GHGFormData | null> {
    const response = await fetch(`${this.baseUrl}/${year}`)

    if (!response.ok) {
      if (response.status === 404) return null
      throw new Error(`Failed to fetch GHG data: ${response.statusText}`)
    }

    const result = await response.json()
    return result.data
  }

  async getAllYears(): Promise<YearlyEmissionData[]> {
    const response = await fetch(`${this.baseUrl}/years`)

    if (!response.ok) {
      throw new Error(`Failed to fetch years: ${response.statusText}`)
    }

    const result = await response.json()
    return result.data || []
  }

  async deleteGHGData(year: number): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${year}`, {
      method: "DELETE",
    })

    if (!response.ok) {
      throw new Error(`Failed to delete GHG data: ${response.statusText}`)
    }
  }
}

export const ghgCalculatorApi = new GHGCalculatorAPI()
```

### Step 2: Update EmissionsContext

Replace localStorage operations in `src/components/ghg-calculator/EmissionsContext.tsx`:

```typescript
import { ghgCalculatorApi } from "@/services/ghgCalculatorApi"

// Replace saveFormData function
const saveFormData = async (year?: number) => {
  try {
    if (!year) {
      throw new Error("Year is required for saving data")
    }

    const dataToSave = {
      ...formData,
      metadata: {
        ...formData.metadata,
        lastSaved: new Date().toISOString(),
      },
    }

    await ghgCalculatorApi.saveGHGData(year, dataToSave)
  } catch (error) {
    console.error("Failed to save GHG Calculator data:", error)
    throw error
  }
}

// Add loadFormData function
const loadFormData = async (year: number): Promise<GHGFormData | null> => {
  try {
    return await ghgCalculatorApi.getGHGData(year)
  } catch (error) {
    console.error(`Failed to load GHG data for year ${year}:`, error)
    return null
  }
}
```

### Step 3: Update Year Calculator Page

Modify `src/app/admin/ghg-calculator/[year]/page.tsx`:

```typescript
// Replace localStorage operations
useEffect(() => {
  const loadData = async () => {
    try {
      const savedData = await ghgCalculatorApi.getGHGData(year)
      if (savedData) {
        setFormData(savedData)
        console.log(`Loaded GHG Calculator data for year ${year}`)
      } else {
        console.log(`No saved data for year ${year}, resetting to default`)
        resetFormData()
      }
    } catch (error) {
      console.error(`Failed to load saved data for year ${year}:`, error)
      resetFormData()
    }
  }

  loadData()
}, [year])

// Replace handleSave function
const handleSave = async () => {
  setIsSaving(true)
  setSaveMessage("")
  try {
    const dataToSave = {
      ...formData,
      metadata: {
        ...formData.metadata,
        lastSaved: new Date().toISOString(),
        year: year,
      },
      calculatedTotals: {
        sectionTotals: sectionTotals,
        scope1Total: scope1Total,
        scope2Total: scope2Total,
        totalEmissions: scope1Total + scope2Total,
      },
    }

    await ghgCalculatorApi.saveGHGData(year, dataToSave)
    setSaveMessage("Data saved successfully!")
    setTimeout(() => setSaveMessage(""), 3000)
  } catch (error) {
    setSaveMessage("Failed to save data. Please try again.")
    setTimeout(() => setSaveMessage(""), 3000)
  } finally {
    setIsSaving(false)
  }
}

// Replace handleReset function
const handleReset = async () => {
  if (confirm("Are you sure you want to reset all data for this year?")) {
    try {
      await ghgCalculatorApi.deleteGHGData(year)
      resetFormData()
      setSaveMessage("Data reset successfully!")
      setTimeout(() => setSaveMessage(""), 3000)
    } catch (error) {
      setSaveMessage("Failed to reset data. Please try again.")
      setTimeout(() => setSaveMessage(""), 3000)
    }
  }
}
```

### Step 4: Update Main Listing Page

Modify `src/app/admin/ghg-calculator/page.tsx`:

```typescript
// Replace localStorage operations
useEffect(() => {
  const loadYearlyData = async () => {
    try {
      const data = await ghgCalculatorApi.getAllYears()
      setYearlyData(data)
    } catch (error) {
      console.error("Failed to load yearly data:", error)
      setYearlyData([])
    }
  }

  loadYearlyData()
}, [])

// Remove calculateScope1Total and calculateScope2Total functions
// (API now returns calculated totals directly)
```

---

## Backend Requirements

### Database Schema

```sql
CREATE TABLE ghg_calculator_data (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,
  year INTEGER NOT NULL,
  data JSONB NOT NULL,
  calculated_totals JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, year)
);

CREATE INDEX idx_ghg_calculator_user_year ON ghg_calculator_data(user_id, year);
CREATE INDEX idx_ghg_calculator_year ON ghg_calculator_data(year);
```

### Authentication & Authorization

- **User Authentication**: Required for all endpoints
- **User Isolation**: Each user can only access their own GHG data
- **Role-based Access**: Admin users can view all data (optional)

### Data Validation

Backend should validate:

- **Year**: Valid 4-digit year (e.g., 2020-2030)
- **Data Structure**: Matches GHGFormData TypeScript interface
- **Calculated Totals**: Verify calculations match form data
- **Required Fields**: Ensure critical fields are present

### Error Handling

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid data format",
    "details": {
      "field": "stationary.fuelRows[0].consumption",
      "issue": "Must be a valid number"
    }
  }
}
```

---

## Migration Strategy

### Phase 1: Parallel Implementation

1. Implement backend APIs
2. Create frontend API service layer
3. Add feature flag for localStorage vs API

### Phase 2: Gradual Migration

1. New data saves to both localStorage and API
2. Reads prioritize API, fallback to localStorage
3. Background sync of existing localStorage data

### Phase 3: Full Migration

1. Remove localStorage operations
2. Remove feature flags
3. Clean up legacy code

### Phase 4: Data Cleanup

1. Clear localStorage data after successful migration
2. Monitor for any remaining localStorage usage
3. Update documentation

---

## Testing Checklist

### API Testing

- [ ] Save GHG data with complete payload
- [ ] Retrieve GHG data for existing year
- [ ] Retrieve GHG data for non-existent year
- [ ] List all years with correct totals
- [ ] Delete GHG data successfully
- [ ] Handle authentication errors
- [ ] Handle validation errors
- [ ] Handle server errors

### Frontend Testing

- [ ] Save functionality works with API
- [ ] Load functionality works with API
- [ ] Reset functionality works with API
- [ ] Main listing shows correct data
- [ ] Error handling displays user-friendly messages
- [ ] Loading states work correctly
- [ ] Offline behavior (if applicable)

### Data Integrity Testing

- [ ] Calculated totals match form data
- [ ] Data persists correctly across sessions
- [ ] Concurrent user access works
- [ ] Large datasets perform well
- [ ] Data migration preserves all information

---

## Security Considerations

1. **Input Validation**: Sanitize all form inputs
2. **SQL Injection**: Use parameterized queries
3. **XSS Protection**: Escape output data
4. **Rate Limiting**: Prevent API abuse
5. **Data Encryption**: Encrypt sensitive data at rest
6. **Audit Logging**: Log all data modifications
7. **Backup Strategy**: Regular data backups

---

## Performance Optimization

1. **Database Indexing**: Index frequently queried fields
2. **Caching**: Cache frequently accessed data
3. **Pagination**: For large datasets
4. **Compression**: Compress large JSON payloads
5. **CDN**: Cache static assets
6. **Connection Pooling**: Optimize database connections

---

## Monitoring & Logging

1. **API Metrics**: Response times, error rates
2. **Database Performance**: Query performance, connection usage
3. **User Activity**: Save/load patterns, error frequency
4. **Data Growth**: Storage usage trends
5. **Error Tracking**: Detailed error logs with context
