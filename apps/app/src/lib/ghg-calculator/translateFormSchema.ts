/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { Column } from "@/components/ghg-calculator/DynamicTable"

// Translation mapping for fuel options
const fuelTranslationMap: Record<string, string> = {
  "Natural gas": "naturalGas",
  "Heating oil": "heatingOil",
  Diesel: "diesel",
  "Coal (domestic)": "coalDomestic",
  "Coal (industrial)": "coalIndustrial",
  LPG: "lpg",
  Propane: "propane",
  "Biomass - wood logs": "biomassWoodLogs",
  "Biomass - wood chips": "biomassWoodChips",
  "Biomass - wood pellets": "biomassWoodPellets",
  "Biomass - grass/straw": "biomassGrassStraw",
}

// Translation mapping for vehicle categories
const vehicleCategoryTranslationMap: Record<string, string> = {
  "Passenger cars": "passengerCars",
  LCVs: "lcvs",
  HGVs: "hgvs",
  "Buses and coaches": "busesAndCoaches",
  Other: "other",
}

// Translation mapping for fuel types
const fuelTypeTranslationMap: Record<string, string> = {
  PETROL: "petrol",
  DIESEL: "diesel",
  LPG: "lpg",
  CNG: "cng",
}

// Translation mapping for gases
const gasTranslationMap: Record<string, string> = {
  R134a: "r134a",
  R404A: "r404a",
  R410A: "r410a",
  R32: "r32",
  SF6: "sf6",
  CH4: "ch4",
  N2O: "n2o",
  CO2: "co2",
}

// Translation mapping for animals
const animalTranslationMap: Record<string, string> = {
  "Dairy Cattle": "dairyCattle",
  "Non-dairy cattle": "nonDairyCattle",
  Sheep: "sheep",
  Swine: "swine",
  Goats: "goats",
  Horses: "horses",
  Poultry: "poultry",
  Rabbit: "rabbit",
}

// Translation mapping for fertiliser types
const fertiliserTypeTranslationMap: Record<string, string> = {
  "Inorganic Nitrogen fertilizers": "inorganicNitrogen",
  "Organic Nitrogen fertilizers": "organicNitrogen",
  Limestone: "limestone",
  Dolomite: "dolomite",
  "Animal manure purposefully applied to soils": "animalManure",
}

// Translation mapping for electricity types
const electricityTypeTranslationMap: Record<string, string> = {
  "non-RES": "nonRes",
  RES: "res",
}

// Translation mapping for district categories
const districtCategoryTranslationMap: Record<string, string> = {
  Heating: "heating",
  Cooling: "cooling",
  Steam: "steam",
}

// Translation mapping for yes/no options
const yesNoTranslationMap: Record<string, string> = {
  YES: "yes",
  NO: "no",
}

// Column label translation mapping
const columnLabelTranslationMap: Record<string, string> = {
  Fuel: "fuel",
  Consumption: "consumption",
  Unit: "unit",
  "Estimation: Area": "area",
  "Area Unit": "areaUnit",
  Category: "category",
  "Fuel Type": "fuelType",
  Distance: "distance",
  "Distance Unit": "distanceUnit",
  "Expense (EUR)": "expense",
  Gas: "name",
  Amount: "amount",
  Animal: "animal",
  Count: "count",
  "% of manure goes into the field?": "fieldPercentage",
  Type: "type",
  "Annual Cost (EUR)": "cost",
  "PPA/Guarantee?": "hasPPA",
  "Annual Consumption": "annualConsumption",
  "Estimation by Area": "estimationByArea",
}

/**
 * Translates dropdown options based on the column ID and option values
 */
export function translateOptions(
  columnId: string,
  options: string[] | { value: string; category: string }[],
  t: (key: string) => string
): string[] {
  // Handle object-based options (like fuel options with categories)
  if (Array.isArray(options) && options.length > 0 && typeof options[0] === "object") {
    const objectOptions = options as { value: string; category: string }[]
    return objectOptions.map((option) => {
      const translationKey = fuelTranslationMap[option.value]
      return translationKey ? t(`options.fuels.${translationKey}`) : option.value
    })
  }

  // Handle string array options
  const stringOptions = options as string[]

  switch (columnId) {
    case "fuel":
      // Handle fuel options when they come as string arrays (filtered by getSelectOptions)
      return stringOptions.map((option) => {
        const translationKey = fuelTranslationMap[option]
        return translationKey ? t(`options.fuels.${translationKey}`) : option
      })

    case "category":
      return stringOptions.map((option) => {
        const translationKey = vehicleCategoryTranslationMap[option] || districtCategoryTranslationMap[option]
        if (translationKey) {
          const categoryType = vehicleCategoryTranslationMap[option] ? "vehicleCategories" : "districtCategories"
          return t(`options.${categoryType}.${translationKey}`)
        }
        return option
      })

    case "fuelType":
      return stringOptions.map((option) => {
        const translationKey = fuelTypeTranslationMap[option]
        return translationKey ? t(`options.fuelTypes.${translationKey}`) : option
      })

    case "name": // Gas column in fugitive emissions
      return stringOptions.map((option) => {
        const translationKey = gasTranslationMap[option]
        return translationKey ? t(`options.gases.${translationKey}`) : option
      })

    case "animal":
      return stringOptions.map((option) => {
        const translationKey = animalTranslationMap[option]
        return translationKey ? t(`options.animals.${translationKey}`) : option
      })

    case "type":
      // Check if it's fertiliser type or electricity type
      if (stringOptions.includes("Inorganic Nitrogen fertilizers")) {
        return stringOptions.map((option) => {
          const translationKey = fertiliserTypeTranslationMap[option]
          return translationKey ? t(`options.fertiliserTypes.${translationKey}`) : option
        })
      } else if (stringOptions.includes("non-RES")) {
        return stringOptions.map((option) => {
          const translationKey = electricityTypeTranslationMap[option]
          return translationKey ? t(`options.electricityTypes.${translationKey}`) : option
        })
      }
      return stringOptions

    case "hasPPA":
      return stringOptions.map((option) => {
        const translationKey = yesNoTranslationMap[option]
        return translationKey ? t(`options.yesNo.${translationKey}`) : option
      })

    case "unit":
    case "distanceUnit":
    case "areaUnit":
      // Units are typically kept as-is (KWH, LITRES, etc.)
      return stringOptions

    default:
      return stringOptions
  }
}

/**
 * Translates column labels
 */
export function translateColumnLabel(label: string, t: (key: string) => string): string {
  const translationKey = columnLabelTranslationMap[label]
  return translationKey ? t(`columnLabels.${translationKey}`) : label
}

/**
 * Translates a column object with its label and options
 */
export function translateColumn(column: Column, t: (key: string) => string): Column {
  return {
    ...column,
    label: translateColumnLabel(column.label, t),
    options: column.options ? translateOptions(column.id, column.options, t) : undefined,
  }
}

/**
 * Translates an array of columns
 */
export function translateColumns(columns: Column[], t: (key: string) => string): Column[] {
  return columns.map((column) => translateColumn(column, t))
}

/**
 * Reverse translation: converts translated value back to original English value
 * This is needed when storing form data - we store English values but display translated ones
 */
export function reverseTranslateValue(
  columnId: string,
  translatedValue: string,
  originalOptions: string[] | { value: string; category: string }[],
  t: (key: string) => string
): string {
  // Handle object-based options (like fuel options with categories)
  if (Array.isArray(originalOptions) && originalOptions.length > 0 && typeof originalOptions[0] === "object") {
    const objectOptions = originalOptions as { value: string; category: string }[]
    for (const option of objectOptions) {
      const translationKey = fuelTranslationMap[option.value]
      if (translationKey && t(`options.fuels.${translationKey}`) === translatedValue) {
        return option.value
      }
    }
    return translatedValue
  }

  // Handle string array options
  const stringOptions = originalOptions as string[]

  // Create reverse mapping by checking each original value's translation
  for (const originalValue of stringOptions) {
    let translatedOriginal = originalValue

    switch (columnId) {
      case "fuel":
        // Handle fuel options when they come as string arrays (filtered by getSelectOptions)
        const fuelTranslationKey = fuelTranslationMap[originalValue]
        if (fuelTranslationKey) {
          translatedOriginal = t(`options.fuels.${fuelTranslationKey}`)
        }
        break

      case "category":
        const vehicleKey = vehicleCategoryTranslationMap[originalValue]
        const districtKey = districtCategoryTranslationMap[originalValue]
        if (vehicleKey) {
          translatedOriginal = t(`options.vehicleCategories.${vehicleKey}`)
        } else if (districtKey) {
          translatedOriginal = t(`options.districtCategories.${districtKey}`)
        }
        break

      case "fuelType":
        const fuelKey = fuelTypeTranslationMap[originalValue]
        if (fuelKey) {
          translatedOriginal = t(`options.fuelTypes.${fuelKey}`)
        }
        break

      case "name": // Gas column
        const gasKey = gasTranslationMap[originalValue]
        if (gasKey) {
          translatedOriginal = t(`options.gases.${gasKey}`)
        }
        break

      case "animal":
        const animalKey = animalTranslationMap[originalValue]
        if (animalKey) {
          translatedOriginal = t(`options.animals.${animalKey}`)
        }
        break

      case "type":
        const fertKey = fertiliserTypeTranslationMap[originalValue]
        const elecKey = electricityTypeTranslationMap[originalValue]
        if (fertKey) {
          translatedOriginal = t(`options.fertiliserTypes.${fertKey}`)
        } else if (elecKey) {
          translatedOriginal = t(`options.electricityTypes.${elecKey}`)
        }
        break

      case "hasPPA":
        const yesNoKey = yesNoTranslationMap[originalValue]
        if (yesNoKey) {
          translatedOriginal = t(`options.yesNo.${yesNoKey}`)
        }
        break
    }

    if (translatedOriginal === translatedValue) {
      return originalValue
    }
  }

  // If no match found, return the translated value as-is
  return translatedValue
}
