/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import React from "react"
import { useTranslations } from "next-intl"
import { Bar, BarChart, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"

import { Card, CardContent, CardHeader, CardTitle } from "@kreios/ui/card"
import { ChartContainer, ChartLegend, ChartLegendContent } from "@kreios/ui/chart"

import { ReportBreakdownTable } from "./ReportBreakdownTable"
import { ReportComparisons } from "./ReportComparisons"
import { ReportHeader } from "./ReportHeader"
import { ReportSummaryCards } from "./ReportSummaryCards"

// Example props type (replace with your actual emissions summary type)
export type EmissionsSummary = {
  scope1: {
    total: number
    stationary: number
    mobile: number
    fugitive: number
    agricultural: number
    biogenic: number
  }
  scope2: {
    total: number
    electricity: number
    district: number
  }
  resPpaAvailable?: boolean
  biomass?: number
}

type ReportSummaryProps = {
  emissions: EmissionsSummary
}

export const ReportSummary: React.FC<ReportSummaryProps> = ({ emissions }) => {
  const t = useTranslations("ghgCalculator.report")
  const totalEmissions = emissions.scope1.total + emissions.scope2.total

  // Comparison calculations
  const trees = Math.round(totalEmissions / 21)
  const kms = Math.round(totalEmissions * 4000)
  const globes = ((totalEmissions * 4000) / 40075).toFixed(2)

  // Number formatter for thousands separator and two decimals
  const formatNumber = (value: number) =>
    new Intl.NumberFormat("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(value)

  // Chart data for Scope 1 and 2 share
  const scopeShareData = [
    { name: "Scope 1", value: emissions.scope1.total, fill: "#2563eb" }, // blue-600
    { name: "Scope 2", value: emissions.scope2.total, fill: "#6b7280" }, // gray-500
  ]
  const scopeShareConfig = {
    value: { label: "Value" },
    "Scope 1": { label: "Scope 1", color: "#2563eb" },
    "Scope 2": { label: "Scope 2", color: "#6b7280" },
  }

  // Chart data for Scope 1 breakdown
  const scope1BreakdownData = [
    { name: t("stationaryEmissions"), value: emissions.scope1.stationary },
    { name: t("nonStationaryEmissions"), value: emissions.scope1.mobile },
    { name: t("fugitiveEmissions"), value: emissions.scope1.fugitive },
    { name: t("agricultural"), value: emissions.scope1.agricultural },
    { name: t("biogenicEmissions"), value: emissions.scope1.biogenic },
  ]

  // Chart data for Scope 2 breakdown
  const scope2BreakdownData = [
    { name: t("electricity"), value: emissions.scope2.electricity },
    { name: t("districtEmissions"), value: emissions.scope2.district },
  ]

  // Calculations

  return (
    <div className="p-2 sm:p-4 md:p-8">
      <ReportHeader year={"2023"} company={"-"} />
      <ReportSummaryCards total={totalEmissions} scope1={emissions.scope1.total} scope2={emissions.scope2.total} />
      <ReportComparisons trees={trees} kms={kms} globes={globes} />
      <ReportBreakdownTable emissions={emissions} resPpaAvailable={emissions.resPpaAvailable ?? false} />
      <hr className="my-8 border-0 border-t-4 border-[#111]" />
      {/* Emissions not included in Scopes 1 and 2 */}
      <div className="my-8 box-border flex w-full flex-col items-start rounded-lg border border-gray-200 bg-[#fafbfc] p-4">
        <div className="mb-2 text-[16px] font-bold tracking-[0.1px]">{t("emissionsNotIncluded")}</div>
        <div className="mb-1 flex w-full flex-wrap items-center gap-5">
          <span className="min-w-[200px] text-[15px]">{t("emissionsFromBiomass")}</span>
          <span className="min-w-[60px] rounded bg-gray-100 px-4 py-1 text-center text-[20px] font-bold tracking-[0.2px] text-[#555]">
            {formatNumber(emissions.biomass ?? 0)}
          </span>
          <span className="ml-2 text-[15px] font-medium text-[#555]">{t("tco2ePerYear")}</span>
        </div>
        <div className="ml-1 mt-0.5 max-w-[900px] text-[13px] italic text-[#888]">{t("biomassNote")}</div>
      </div>

      {/* Charts Section - moved above GHG Emissions Report */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>{t("shareOfScope1And2")}</CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer config={scopeShareConfig} className="aspect-auto h-[250px]">
            <PieChart width={300} height={250}>
              <Pie
                data={scopeShareData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={80}
                label={({ value }) => formatNumber(Number(value))}
              />
              <ChartLegend content={<ChartLegendContent nameKey="name" />} className="flex-wrap gap-2" />
            </PieChart>
          </ChartContainer>
        </CardContent>
      </Card>
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>{t("breakdownOfScope1")}</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={scope1BreakdownData} margin={{ top: 40, right: 30, left: 20, bottom: 60 }}>
              <XAxis dataKey="name" tick={{ fontSize: 12 }} interval={0} angle={-45} textAnchor="end" height={80} />
              <YAxis tickFormatter={formatNumber} tick={{ fontSize: 12 }} />
              <Tooltip formatter={(value) => [formatNumber(Number(value)), "tCO₂e"]} />
              <Bar
                dataKey="value"
                fill="#2563eb"
                radius={[4, 4, 0, 0]}
                maxBarSize={80}
                label={{ position: "top", formatter: (value: number) => formatNumber(value) }}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>{t("breakdownOfScope2")}</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={scope2BreakdownData} margin={{ top: 40, right: 30, left: 20, bottom: 60 }}>
              <XAxis dataKey="name" tick={{ fontSize: 12 }} interval={0} angle={-45} textAnchor="end" height={80} />
              <YAxis tickFormatter={formatNumber} tick={{ fontSize: 12 }} />
              <Tooltip formatter={(value) => [formatNumber(Number(value)), "tCO₂e"]} />
              <Bar
                dataKey="value"
                fill="#2563eb"
                radius={[4, 4, 0, 0]}
                maxBarSize={80}
                label={{ position: "top", formatter: (value: number) => formatNumber(value) }}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* <h1 style={{ fontSize: 32, fontWeight: 700, marginBottom: 24 }}>GHG Emissions Report</h1>
      <div
        style={{
          display: "flex",
          gap: 32,
          marginBottom: 32,
          flexWrap: "wrap",
        }}
      >
        <div style={{ flex: 1, minWidth: 250 }}>
          <h2>Total Scope 1</h2>
          <div style={{ fontSize: 24, fontWeight: 600 }}>{emissions.scope1.total.toFixed(2)} tCO₂e/year</div>
        </div>
        <div style={{ flex: 1, minWidth: 250 }}>
          <h2>Total Scope 2</h2>
          <div style={{ fontSize: 24, fontWeight: 600 }}>{emissions.scope2.total.toFixed(2)} tCO₂e/year</div>
        </div>
      </div> */}

      {/* Add more tables, comparisons, and breakdowns as needed */}
    </div>
  )
}
