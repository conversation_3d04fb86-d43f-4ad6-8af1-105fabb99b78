/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import type { MobileRow } from "@/lib/ghg-calculator/types"
import { useEffect, useState } from "react"
import { useEmissions } from "@/components/ghg-calculator/EmissionsContext"
import {
  calculateMobileDistanceEmissions,
  calculateMobileExpenseEmissions,
  calculateMobileFuelEmissions,
} from "@/lib/ghg-calculator/calc-logic"
import { formSchema } from "@/lib/ghg-calculator/formSchema"
import { formatNumber } from "@/utils/formatNumber"
import { useTranslations } from "next-intl"

import { But<PERSON> } from "@kreios/ui/button"

import type { Column } from "./DynamicTable"
import { DynamicTable } from "./DynamicTable"

interface MobileEmissionsFormProps {
  onTotalChange?: (total: number) => void
}

export default function MobileEmissionsForm({ onTotalChange }: MobileEmissionsFormProps) {
  const t = useTranslations("ghgCalculator.forms.mobile")
  const tCommon = useTranslations("ghgCalculator.forms.common")
  const { setEmissions, formData, setFormData } = useEmissions()
  const mobileSchema = formSchema.sections.find((s) => s.id === "scope1_mobile")

  const columns = mobileSchema?.columns ?? []

  // Helper to get default row values from schema columns
  const getDefaultRow = (): MobileRow => {
    const row: Record<string, string> = {}
    for (const col of columns) {
      if (col.type === "select") {
        row[col.id] = (col.options as string[])[0] || ""
      } else {
        row[col.id] = ""
      }
    }
    return row as MobileRow
  }

  const [rows, setRows] = useState<MobileRow[]>(formData.mobile)

  // Sync local state with context when formData changes
  useEffect(() => {
    setRows(formData.mobile)
  }, [formData.mobile])

  const addRow = () => {
    const newRows = [...rows, getDefaultRow()]
    setRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      mobile: newRows,
    }))
  }
  const deleteRow = (index: number) => {
    const newRows = rows.filter((_, i) => i !== index)
    setRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      mobile: newRows,
    }))
  }
  const update = <K extends keyof MobileRow>(i: number, key: K, val: MobileRow[K]) => {
    const updated: MobileRow[] = [...rows]
    updated[i][key] = val
    // Mutually exclusive: if entering consumption, clear distance/expense; if entering distance, clear others, etc.
    if (key === "consumption") {
      updated[i].distance = ""
      updated[i].distanceUnit = ""
      updated[i].expense = ""
    }
    if (key === "distance" || key === "distanceUnit") {
      updated[i].consumption = ""
      updated[i].unit = ""
      updated[i].expense = ""
    }
    if (key === "expense") {
      updated[i].consumption = ""
      updated[i].unit = ""
      updated[i].distance = ""
      updated[i].distanceUnit = ""
    }
    setRows(updated)
    // Update context
    setFormData((prev) => ({
      ...prev,
      mobile: updated,
    }))
  }

  const calculateEmissions = (row: MobileRow): string => {
    // Only one method per row, based on which field is filled
    if (row.consumption && row.unit && row.fuelType) {
      const consumption = parseFloat(row.consumption)
      if (!isNaN(consumption)) {
        return formatNumber(calculateMobileFuelEmissions(row.fuelType, consumption, row.unit))
      }
    } else if (row.distance && row.distanceUnit) {
      const distance = parseFloat(row.distance)
      if (!isNaN(distance)) {
        return formatNumber(calculateMobileDistanceEmissions(row.category, distance, row.distanceUnit))
      }
    } else if (row.expense) {
      const expense = parseFloat(row.expense)
      if (!isNaN(expense)) {
        return formatNumber(calculateMobileExpenseEmissions(expense))
      }
    }
    return "-"
  }

  const total = rows.reduce((sum, row) => {
    if (row.consumption && row.unit && row.fuelType) {
      const consumption = parseFloat(row.consumption)
      if (!isNaN(consumption)) {
        return sum + calculateMobileFuelEmissions(row.fuelType, consumption, row.unit)
      }
    } else if (row.distance && row.distanceUnit) {
      const distance = parseFloat(row.distance)
      if (!isNaN(distance)) {
        return sum + calculateMobileDistanceEmissions(row.category, distance, row.distanceUnit)
      }
    } else if (row.expense) {
      const expense = parseFloat(row.expense)
      if (!isNaN(expense)) {
        return sum + calculateMobileExpenseEmissions(expense)
      }
    }
    return sum
  }, 0)

  useEffect(() => {
    if (onTotalChange) onTotalChange(total)
    setEmissions((prev) => ({
      ...prev,
      scope1: {
        ...prev.scope1,
        mobile: total,
        total: prev.scope1.stationary + total + prev.scope1.fugitive + prev.scope1.agricultural + prev.scope1.biogenic,
      },
    }))
  }, [total])

  return (
    <div>
      <p className="mb-2 rounded border border-green-200 bg-green-50 px-3 py-2 text-sm font-medium text-primary">
        {t("description")}
      </p>
      <p className="mb-4 text-sm text-gray-700">{mobileSchema?.description}</p>
      <div className="mb-8 overflow-x-auto">
        <DynamicTable<MobileRow>
          columns={
            columns.map((col) => ({
              ...col,
              type: col.type as "number" | "select" | "text" | "action",
            })) as Column[]
          }
          rows={rows}
          onRowChange={(rowIndex, key, value) => update(rowIndex, key, value as string)}
          onRowDelete={deleteRow}
          getSelectOptions={(col) => col.options!}
          extraColumns={[
            (row: MobileRow) => <span className="text-right font-semibold">{calculateEmissions(row)}</span>,
          ]}
          extraColumnLabels={[tCommon("total", { total: "" }).replace(": ", " (tCO₂e)")]}
          renderCell={(col, row) => {
            const hasConsumption = !!row.consumption
            const hasDistance = !!row.distance
            const hasExpense = !!row.expense
            // N/A logic for mutually exclusive fields
            if (col.id === "fuelType" && !hasConsumption) {
              return <span className="text-gray-400"></span>
            }
            if (col.id === "unit" && !hasConsumption) {
              return <span className="text-gray-400"></span>
            }
            if (col.id === "consumption" && (hasDistance || hasExpense)) {
              return <span className="text-gray-400"></span>
            }
            if (col.id === "distance" && (hasConsumption || hasExpense)) {
              return <span className="text-gray-400"></span>
            }
            if (col.id === "distanceUnit" && !hasDistance) {
              return <span className="text-gray-400"></span>
            }
            if (col.id === "expense" && (hasConsumption || hasDistance)) {
              return <span className="text-gray-400"></span>
            }
            return undefined
          }}
        />
        <div className="my-4 flex items-center justify-between">
          <span className="text-base font-bold text-primary">{tCommon("total", { total: formatNumber(total) })}</span>
          <Button onClick={addRow}>{tCommon("addRow")}</Button>
        </div>
      </div>
    </div>
  )
}
