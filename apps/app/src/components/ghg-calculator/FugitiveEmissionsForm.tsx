/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import type { FugitiveRow } from "@/lib/ghg-calculator/types"
import { useEffect, useState } from "react"
import { useEmissions } from "@/components/ghg-calculator/EmissionsContext"
import { calculateFugitiveEmissions } from "@/lib/ghg-calculator/calc-logic"
import { formSchema } from "@/lib/ghg-calculator/formSchema"
import { formatNumber } from "@/utils/formatNumber"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"

import type { Column } from "./DynamicTable"
import { DynamicTable } from "./DynamicTable"

interface FugitiveEmissionsFormProps {
  onTotalChange?: (total: number) => void
}

export default function FugitiveEmissionsForm({ onTotalChange }: FugitiveEmissionsFormProps) {
  const t = useTranslations("ghgCalculator.forms.fugitive")
  const tCommon = useTranslations("ghgCalculator.forms.common")
  const { setEmissions, formData, setFormData } = useEmissions()
  const fugitiveSchema = formSchema.sections.find((s) => s.id === "scope1_fugitive")

  const columns = fugitiveSchema?.columns ?? []

  // Helper to get default row values from schema columns
  const getDefaultRow = (): FugitiveRow => {
    const row: Record<string, string> = {}
    for (const col of columns) {
      if (col.type === "select") {
        row[col.id] = (col.options as string[])[0] || ""
      } else {
        row[col.id] = ""
      }
    }
    return row as FugitiveRow
  }

  // Initialize from context
  const [rows, setRows] = useState<FugitiveRow[]>(formData.fugitive)

  // Sync local state with context when formData changes
  useEffect(() => {
    setRows(formData.fugitive)
  }, [formData.fugitive])

  const addRow = () => {
    const newRows = [...rows, getDefaultRow()]
    setRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      fugitive: newRows,
    }))
  }

  const deleteRow = (index: number) => {
    const newRows = rows.filter((_, i) => i !== index)
    setRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      fugitive: newRows,
    }))
  }

  const updateRow = (index: number, key: keyof FugitiveRow, value: string) => {
    const updated = [...rows]
    updated[index][key] = value
    setRows(updated)
    // Update context
    setFormData((prev) => ({
      ...prev,
      fugitive: updated,
    }))
  }

  // Calculate section total
  const total = rows.reduce((sum, row) => {
    const amountNum = parseFloat(row.amount ?? "")
    if (row.name && !isNaN(amountNum)) {
      return sum + calculateFugitiveEmissions(row.name, amountNum, row.unit)
    }
    return sum
  }, 0)

  useEffect(() => {
    if (onTotalChange) onTotalChange(total)
    setEmissions((prev) => ({
      ...prev,
      scope1: {
        ...prev.scope1,
        fugitive: total,
        total: prev.scope1.stationary + prev.scope1.mobile + total + prev.scope1.agricultural + prev.scope1.biogenic,
      },
    }))
  }, [total])

  const filteredColumns = columns.map((col) => ({
    ...col,
    type: col.type as "number" | "select" | "text" | "action",
  })) as Column[]

  return (
    <div>
      <p className="mb-2 rounded border border-green-200 bg-green-50 px-3 py-2 text-sm font-medium text-primary">
        {t("annualConsumptionNote")}
      </p>
      <p className="mb-4 text-sm text-gray-700">{t("description")}</p>
      <div className="mb-4 overflow-x-auto">
        <DynamicTable<FugitiveRow>
          columns={filteredColumns}
          rows={rows}
          onRowChange={(rowIndex, key, value) => updateRow(rowIndex, key, value as string)}
          onRowDelete={deleteRow}
          getSelectOptions={(col) => col.options!}
          extraColumns={[
            (row: FugitiveRow) => {
              const amountNum = parseFloat(row.amount ?? "")
              const isValid = row.name && !isNaN(amountNum)
              return isValid ? formatNumber(calculateFugitiveEmissions(row.name, amountNum, row.unit)) : "-"
            },
          ]}
          extraColumnLabels={[tCommon("totalEmissionsColumn")]}
          renderCell={() => undefined}
        />
        <div className="my-4 flex items-center justify-between">
          <span className="text-base font-bold text-primary">{tCommon("total", { total: formatNumber(total) })}</span>
          <Button onClick={addRow}>{tCommon("addRow")}</Button>
        </div>
      </div>
    </div>
  )
}
