/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import { useEffect, useState } from "react"
import { useEmissions } from "@/components/ghg-calculator/EmissionsContext"
import { scope2DistrictEnergyConfig, toKWh } from "@/lib/ghg-calculator/calc-logic"
import { formSchema } from "@/lib/ghg-calculator/formSchema"
import { formatNumber } from "@/utils/formatNumber"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"

import type { Column } from "./DynamicTable"
import { DynamicTable } from "./DynamicTable"

const schema = formSchema.sections.find((s) => s.id === "scope2_district")
if (!schema) throw new Error("Missing schema for scope2_district")
const columns = schema.columns
const selectOptionsMap = Object.fromEntries(
  columns.filter((col) => col.type === "select").map((col) => [col.id, col.options as string[]])
)

type DistrictEnergyRow = {
  category: string
  consumption: string
  unit: string
  area: string
  areaUnit: string
  [key: string]: unknown
}

const emissionFactors = scope2DistrictEnergyConfig.emissionFactors
const areaIntensity = scope2DistrictEnergyConfig.areaIntensity

export default function DistrictEnergyForm({ onTotalChange }: { onTotalChange?: (total: number) => void }) {
  const t = useTranslations("ghgCalculator.forms.district")
  const tCommon = useTranslations("ghgCalculator.forms.common")
  const { setEmissions, formData, setFormData } = useEmissions()
  // Initialize from context
  const [rows, setRows] = useState<DistrictEnergyRow[]>(formData.district)

  // Sync local state with context when formData changes
  useEffect(() => {
    setRows(formData.district)
  }, [formData.district])

  const getDefaultRow = (): DistrictEnergyRow => {
    return {
      category: selectOptionsMap.category[0] || "",
      consumption: "",
      unit: selectOptionsMap.unit[0] || "",
      area: "",
      areaUnit: selectOptionsMap.areaUnit[0] || "",
    }
  }

  const addRow = () => {
    const newRows = [...rows, getDefaultRow()]
    setRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      district: newRows,
    }))
  }

  const deleteRow = (index: number) => {
    const newRows = rows.filter((_, i) => i !== index)
    setRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      district: newRows,
    }))
  }

  const updateRow = (i: number, key: keyof DistrictEnergyRow, value: string) => {
    const updated = [...rows]
    if (typeof key === "string" && key in updated[i]) {
      ;(updated[i] as unknown as Record<string, unknown>)[key] = value
    }
    // Mutually exclusive: if entering consumption, clear area; if entering area, clear consumption
    if (key === "consumption" || key === "unit") {
      updated[i].area = ""
      updated[i].areaUnit = selectOptionsMap.areaUnit[0] || ""
    }
    if (key === "area" || key === "areaUnit") {
      updated[i].consumption = ""
      updated[i].unit = selectOptionsMap.unit[0] || ""
    }
    setRows(updated)
    // Update context
    setFormData((prev) => ({
      ...prev,
      district: updated,
    }))
  }

  const calculateEmissions = (row: DistrictEnergyRow) => {
    // Area fallback
    if (!row.consumption && row.area) {
      let area = parseFloat(row.area)
      if (!isNaN(area) && area > 0) {
        // Convert ft² to m² if needed - normalize unit case
        const normalizedAreaUnit = row.areaUnit.toUpperCase()
        if (normalizedAreaUnit === "FT2") {
          area = area * 0.092903
        }
        // Type guard for category
        const cat = row.category
        if (cat === "Heating" || cat === "Cooling" || cat === "Steam") {
          const kWh = area * areaIntensity[cat]
          const ef = emissionFactors[cat]
          return +((kWh * ef) / 1000).toFixed(3) // tCO2e
        }
      }
      return 0
    }
    // Consumption
    const val = parseFloat(row.consumption)
    if (!isNaN(val) && val > 0) {
      // Type guard for category
      const cat = row.category
      if (cat === "Heating" || cat === "Cooling" || cat === "Steam") {
        const kWh = toKWh(val, row.unit)
        const ef = emissionFactors[cat]
        return +((kWh * ef) / 1000).toFixed(3) // tCO2e
      }
    }
    return 0
  }

  const total = rows.reduce((sum, row) => sum + calculateEmissions(row), 0)

  useEffect(() => {
    if (onTotalChange) onTotalChange(total)
    setEmissions((prev) => ({
      ...prev,
      scope2: {
        ...prev.scope2,
        district: total,
        total: prev.scope2.electricity + total,
      },
    }))
  }, [total])

  const filteredColumns = columns.map((col) => ({
    ...col,
    type: col.type as "number" | "select" | "text" | "action",
  })) as Column[]

  return (
    <div>
      <p className="mb-2 rounded border border-green-200 bg-green-50 px-3 py-2 text-sm font-medium text-primary">
        {t("annualConsumptionNote")}
      </p>
      <p className="mb-4 text-sm text-gray-700">{schema?.description}</p>
      <div className="mb-4 overflow-x-auto">
        <DynamicTable<DistrictEnergyRow>
          columns={filteredColumns}
          rows={rows}
          onRowChange={(rowIndex, key, value) => updateRow(rowIndex, key, value as string)}
          onRowDelete={deleteRow}
          getSelectOptions={(col) => {
            return col.options!
          }}
          extraColumns={[
            (row: DistrictEnergyRow) => {
              const val = calculateEmissions(row)
              return val ? formatNumber(val) : "-"
            },
          ]}
          extraColumnLabels={["Total emissions (tCO₂e)"]}
          renderCell={(col, row) => {
            const hasConsumption = !!row.consumption
            const hasArea = !!row.area
            if ((col.id === "consumption" || col.id === "unit") && hasArea) {
              return <span className="text-gray-400"></span>
            }
            if ((col.id === "area" || col.id === "areaUnit") && hasConsumption) {
              return <span className="text-gray-400"></span>
            }
            return undefined
          }}
        />
        <div className="my-4 flex items-center justify-between">
          <span className="text-base font-bold text-primary">{tCommon("total", { total: formatNumber(total) })}</span>
          <Button onClick={addRow}>{tCommon("addRow")}</Button>
        </div>
      </div>
    </div>
  )
}
