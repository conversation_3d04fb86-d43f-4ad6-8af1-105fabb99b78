/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { AnimalRow, FertiliserRow } from "@/lib/ghg-calculator/types"
import { useEffect, useState } from "react"
import { useEmissions } from "@/components/ghg-calculator/EmissionsContext"
import { calculateEntericFermentation, calculateFertiliserEmissions } from "@/lib/ghg-calculator/calc-logic"
import { formSchema } from "@/lib/ghg-calculator/formSchema"
import { formatNumber } from "@/utils/formatNumber"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"

import type { Column } from "./DynamicTable"
import { DynamicTable } from "./DynamicTable"

const agriSchema = formSchema.sections.find((s) => s.id === "agriculture")

interface AgriculturalEmissionsFormProps {
  onTotalChange?: (total: number) => void
}

export default function AgriculturalEmissionsForm({ onTotalChange }: AgriculturalEmissionsFormProps) {
  const t = useTranslations("ghgCalculator.forms.agricultural")
  const tCommon = useTranslations("ghgCalculator.forms.common")
  const { setEmissions, formData, setFormData } = useEmissions()
  const animalColumns = agriSchema?.columns ?? []
  const fertiliserColumns = agriSchema?.fertiliserColumns ?? []

  const [showForm, setShowForm] = useState(false)
  // Dynamic rows for animals and fertilisers - initialize from context
  const animalOptions = animalColumns.find((c) => c.id === "animal")?.options ?? []
  const [animals, setAnimals] = useState<AnimalRow[]>(formData.agricultural.animalRows)
  const [fertiliserRows, setFertiliserRows] = useState<FertiliserRow[]>(formData.agricultural.fertiliserRows)

  // Sync local state with context when formData changes
  useEffect(() => {
    setAnimals(formData.agricultural.animalRows)
    setFertiliserRows(formData.agricultural.fertiliserRows)
  }, [formData.agricultural.animalRows, formData.agricultural.fertiliserRows])

  const addAnimalRow = () => {
    const newRows = [
      ...animals,
      {
        animal: typeof animalOptions[0] === "string" ? animalOptions[0] : "",
        count: "",
        fieldPercentage: "",
      } as AnimalRow,
    ]
    setAnimals(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      agricultural: {
        ...prev.agricultural,
        animalRows: newRows,
      },
    }))
  }
  const deleteAnimalRow = (index: number) => {
    const newRows = animals.filter((_, i) => i !== index)
    setAnimals(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      agricultural: {
        ...prev.agricultural,
        animalRows: newRows,
      },
    }))
  }
  const updateAnimal = (i: number, key: keyof AnimalRow, value: string) => {
    const updated = [...animals]
    updated[i][key] = value as unknown as AnimalRow[typeof key]
    setAnimals(updated)
    // Update context
    setFormData((prev) => ({
      ...prev,
      agricultural: {
        ...prev.agricultural,
        animalRows: updated,
      },
    }))
  }

  const fertiliserTypeOptions = fertiliserColumns.find((c) => c.id === "type")?.options ?? []
  const fertiliserUnitOptions = fertiliserColumns.find((c) => c.id === "unit")?.options ?? []
  const addFertiliserRow = () => {
    const newRows = [
      ...fertiliserRows,
      {
        type: typeof fertiliserTypeOptions[0] === "string" ? fertiliserTypeOptions[0] : "",
        amount: "",
        unit: typeof fertiliserUnitOptions[0] === "string" ? fertiliserUnitOptions[0] : "",
      } as FertiliserRow,
    ]
    setFertiliserRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      agricultural: {
        ...prev.agricultural,
        fertiliserRows: newRows,
      },
    }))
  }
  const deleteFertiliserRow = (index: number) => {
    const newRows = fertiliserRows.filter((_, i) => i !== index)
    setFertiliserRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      agricultural: {
        ...prev.agricultural,
        fertiliserRows: newRows,
      },
    }))
  }
  const updateFertiliser = (i: number, key: keyof FertiliserRow, value: string) => {
    const updated = [...fertiliserRows]
    updated[i][key] = value as unknown as FertiliserRow[typeof key]
    setFertiliserRows(updated)
    // Update context
    setFormData((prev) => ({
      ...prev,
      agricultural: {
        ...prev.agricultural,
        fertiliserRows: updated,
      },
    }))
  }

  // Calculate section total
  const entericTotal = animals.reduce((sum, animal) => {
    const count = parseFloat(animal.count)
    if (!isNaN(count) && count > 0) {
      return sum + calculateEntericFermentation(animal.animal, count)
    }
    return sum
  }, 0)

  const fertiliserTotal = fertiliserRows.reduce((sum, fertiliser) => {
    const amount = parseFloat(fertiliser.amount)
    if (!isNaN(amount) && amount > 0) {
      return sum + calculateFertiliserEmissions(fertiliser.type, amount)
    }
    return sum
  }, 0)

  const total = entericTotal + fertiliserTotal

  // Call onTotalChange when total changes
  useEffect(() => {
    if (onTotalChange) onTotalChange(total)
    setEmissions((prev) => ({
      ...prev,
      scope1: {
        ...prev.scope1,
        agricultural: total,
        total: prev.scope1.stationary + prev.scope1.mobile + prev.scope1.fugitive + total + prev.scope1.biogenic,
      },
    }))
  }, [total])

  const filteredAnimalColumns = animalColumns.map((col) => ({
    ...col,
    type: col.type as "number" | "select" | "text" | "action",
  })) as Column[]
  const filteredFertiliserColumns = fertiliserColumns.map((col) => ({
    ...col,
    type: col.type as "number" | "select" | "text" | "action",
  })) as Column[]

  return (
    <div className="mt-10 border-t pt-8">
      <h2 className="mb-4 text-xl font-bold">Does your company operate in the agricultural industry?</h2>
      <div className="mb-6">
        <label className="pointer mr-4">
          <input type="radio" checked={!showForm} onChange={() => setShowForm(false)} className="mr-2" />
          No
        </label>
        <label className="pointer ml-4">
          <input type="radio" checked={showForm} onChange={() => setShowForm(true)} className="mr-2" />
          Yes
        </label>
      </div>

      {showForm && (
        <>
          <h2 className="mb-2 text-2xl font-bold">{agriSchema?.title}</h2>
          <p className="mb-2 rounded border border-green-200 bg-green-50 px-3 py-2 text-sm font-medium text-primary">
            {t("description")}
          </p>
          <p className="mb-6 text-sm text-gray-700">{agriSchema?.description}</p>

          <h3 className="mb-2 text-lg font-semibold">{t("animalsTitle")}</h3>
          <div className="mb-6 overflow-x-auto">
            <DynamicTable<AnimalRow>
              columns={filteredAnimalColumns}
              rows={animals}
              onRowChange={(rowIndex, key, value) => updateAnimal(rowIndex, key, value as string)}
              onRowDelete={deleteAnimalRow}
              getSelectOptions={(col) =>
                col.options && Array.isArray(col.options)
                  ? col.options.filter((o): o is string => typeof o === "string")
                  : []
              }
              extraColumns={[
                (row: AnimalRow) => {
                  const count = parseFloat(row.count)
                  return !isNaN(count) && count > 0
                    ? formatNumber(calculateEntericFermentation(row.animal, count))
                    : "-"
                },
              ]}
              extraColumnLabels={[tCommon("total", { total: "" }).replace(": ", " (tCO₂e)")]}
              renderCell={() => undefined}
            />
            <div className="my-4 flex items-center justify-between">
              <span className="text-base font-bold text-primary">
                {tCommon("total", { total: formatNumber(entericTotal) })}
              </span>
              <Button onClick={addAnimalRow}>{tCommon("addRow")}</Button>
            </div>
          </div>

          <h3 className="mb-2 text-lg font-semibold">{t("fertilisersTitle")}</h3>
          <div className="mb-8 overflow-x-auto">
            <DynamicTable<FertiliserRow>
              columns={filteredFertiliserColumns}
              rows={fertiliserRows}
              onRowChange={(rowIndex, key, value) => updateFertiliser(rowIndex, key, value as string)}
              onRowDelete={deleteFertiliserRow}
              getSelectOptions={(col) =>
                col.options && Array.isArray(col.options)
                  ? col.options.filter((o): o is string => typeof o === "string")
                  : []
              }
              extraColumns={[
                (row: FertiliserRow) => {
                  const amount = parseFloat(row.amount)
                  return !isNaN(amount) && amount > 0
                    ? formatNumber(calculateFertiliserEmissions(row.type, amount))
                    : "-"
                },
              ]}
              extraColumnLabels={[tCommon("total", { total: "" }).replace(": ", " (tCO₂e)")]}
              renderCell={() => undefined}
            />
            <div className="my-4 flex items-center justify-between">
              <span className="text-base font-bold text-primary">
                {tCommon("total", { total: formatNumber(fertiliserTotal) })}
              </span>
              <Button onClick={addFertiliserRow}>{tCommon("addRow")}</Button>
            </div>
          </div>
        </>
      )}
      {showForm && (
        <div className="mt-4 text-right font-bold">
          <div>
            {tCommon("entericFermentation")}: {formatNumber(entericTotal)} tCO₂e
          </div>
          <div>
            {tCommon("fertiliserUse")}: {formatNumber(fertiliserTotal)} tCO₂e
          </div>
        </div>
      )}
    </div>
  )
}
